using gameStore.Api.Data;
using gameStore.Api.Dtos;
using gameStore.Api.Endpoints;
using Microsoft.AspNetCore.SignalR.Protocol;

var builder = WebApplication.CreateBuilder(args);

var constString = builder.Configuration.GetConnectionString("GameStore");
builder.Services.AddSqlite<GameStoreContext>(constString);

builder.Services.AddRazorPages();
var app = builder.Build();

app.MapRazorPages();
app.MapGamesEndpoints();

app.MigrateDb();
app.Run();
