using System;
using gameStore.Api.Dtos;
using gameStore.Api.Entities;

namespace gameStore.Api.Data;

public static class GameMapping
{
  public static Game ToEntity(this CreateGameDto game)
{
    return new Game()
    {
        Name = game.Name,
        GenreId = game.GenreId,
        Price = game.Price,
        ReleaseDate = game.ReleaseDate
    };
}


    public static GameSummaryDto ToGameSummaryDto(this Game game)
    {
        return new(
           game.Id,
            game.Name,
            game.Genre?.Name,
            game.Price,
            game.ReleaseDate
        );
    }

        public static GameDetailsDto ToGameDetailsDto(this Game game)
    {
        return new(
           game.Id,
            game.Name,
            game.Genre?.Name,
            game.Price,
            game.ReleaseDate
        );
    }

}
