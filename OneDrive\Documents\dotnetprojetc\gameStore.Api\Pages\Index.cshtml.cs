using gameStore.Api.Data;
using gameStore.Api.Dtos;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;

namespace gameStore.Api.Pages.Games;

public class IndexModel : PageModel
{
    private readonly GameStoreContext _context;

    public IndexModel(GameStoreContext context)
    {
        _context = context;
    }

    public List<GameDetailsDto> Games { get; set; } = [];

    public async Task OnGetAsync()
    {
        Games = await _context.Games
            .Include(g => g.Genre)
            .Select(g => g.ToGameDetailsDto()) // ou ToGameDetailsDto()
            .ToListAsync();
    }
}
