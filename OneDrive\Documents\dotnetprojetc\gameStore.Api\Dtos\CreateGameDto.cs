using System.ComponentModel.DataAnnotations;
using gameStore.Api.Entities;
using Microsoft.AspNetCore.Mvc;

namespace gameStore.Api.Dtos;

public class CreateGameDto
{
    [Required]
    [StringLength(50)]
    public string Name { get; set; } = "";

    [Required]
    public int GenreId { get; set; }

    [Required]
    [Range(1, 100)]
    public decimal Price { get; set; }

    [Required]
    public DateOnly ReleaseDate { get; set; }

   
}

