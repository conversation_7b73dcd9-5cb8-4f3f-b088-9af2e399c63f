@page
@model gameStore.Api.Pages.Games.IndexModel
@{
    Layout = null; // optionnel
}
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>@ViewData["Title"]</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" />
</head>
<body>
    <h1 class="text-primary text-center">@ViewData["Title"]</h1>
    <h1>liste de jeu</h1>
<form method="get" asp-page="/Create" class="mb-3">
    <button type="submit" class="btn btn-success">Ajouter un nouveau jeu</button>
</form>
    <div class="container mt-4">
        <table class="table table-bordered table-striped table-hover">
            <thead class="table-light">
                <tr>
                    <th scope="col">Nom</th>
                    <th scope="col">Genre</th>
                    <th scope="col">Prix</th>
                    <th scope="col">Date de sortie</th>
                    <th scope="col">Action</th>
                </tr>
            </thead>
            <tbody>
            @foreach (var game in Model.Games)
            {
                <tr>
                    <td>@game.Name</td>
                    <td>@game.Genre</td>
                    <td>@game.Price.ToString("C")</td>
                    <td>@game.ReleaseDate.ToString("yyyy-MM-dd")</td>
                    <td>
                        <a  href="/games/delete/@game.Id" class="btn btn-danger mb-3">Suprimer</a>
                        <a  href="/games/edit/@game.Id"   class="btn btn-primary mb-3">modifier</a>

                    </td>
                </tr>
            }
            </tbody>
        </table>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
