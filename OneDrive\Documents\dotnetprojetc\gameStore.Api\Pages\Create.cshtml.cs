using gameStore.Api.Data;
using gameStore.Api.Dtos;
using gameStore.Api.Entities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;

namespace gameStore.Api.Pages
{
    public class CreateModel : PageModel
    {
        private readonly GameStoreContext _context;

        public CreateModel(GameStoreContext context)
        {
            _context = context;
        }
        public CreateGameDto Game { get; set; } = new();
        public List<Genre> Genres { get; set; } = new();

        public async Task OnGetAsync()
        {
            Genres = await _context.Genres.ToListAsync();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
                return Page();

            var newGame = Game.ToEntity();
            _context.Games.Add(newGame);
            await _context.SaveChangesAsync();

            return RedirectToPage("./Index");
        }
    }
}
