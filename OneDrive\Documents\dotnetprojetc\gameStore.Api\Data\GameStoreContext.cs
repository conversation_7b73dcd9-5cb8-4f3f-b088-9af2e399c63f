
using gameStore.Api.Entities;
using Microsoft.EntityFrameworkCore;
namespace gameStore.Api.Data;

public class GameStoreContext(DbContextOptions<GameStoreContext> options)
    : DbContext(options)
{
    public DbSet<Game> Games => Set<Game>();
    public DbSet<Genre> Genres => Set<Genre>();

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Genre>().HasData(
            new {Id=1,Name="test"},
            new {Id=2,Name="test2"},
            new {Id=3,Name="test3"}

        );
    }
}