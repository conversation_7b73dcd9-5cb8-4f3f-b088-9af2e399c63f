@page
@model gameStore.Api.Pages.CreateModel
@{
    ViewData["Title"] = "Ajouter un jeu";
}

<h1 class="text-center text-primary">Ajouter un nouveau jeu</h1>

<form method="post">
    <div class="mb-3">
        <label asp-for="Game.Name" class="form-label">Nom</label>
        <input asp-for="Game.Name" class="form-control" />
    </div>
    <div class="mb-3">
        <label asp-for="Game.GenreId" class="form-label">Genre</label>
        <select asp-for="Game.GenreId" class="form-select">
            @foreach (var genre in Model.Genres)
            {
                <option value="@genre.Id">@genre.Name</option>
            }
        </select>
    </div>
    <div class="mb-3">
        <label asp-for="Game.Price" class="form-label">Prix</label>
        <input asp-for="Game.Price" class="form-control" type="number" step="0.01" />
    </div>
    <div class="mb-3">
        <label asp-for="Game.ReleaseDate" class="form-label">Date de sortie</label>
        <input asp-for="Game.ReleaseDate" class="form-control" type="date" />
    </div>
    <button type="submit" class="btn btn-success">Ajouter</button>
    <a asp-page="./Index" class="btn btn-secondary">Annuler</a>
</form>

