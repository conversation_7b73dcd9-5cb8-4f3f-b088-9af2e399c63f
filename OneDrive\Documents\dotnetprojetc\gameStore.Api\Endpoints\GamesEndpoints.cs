using System;
using gameStore.Api.Data;
using gameStore.Api.Dtos;
using gameStore.Api.Entities;
using Microsoft.EntityFrameworkCore;

namespace gameStore.Api.Endpoints;

public static class GamesEndpoints
{
    const string GetGameEndpointName = "GetGame";

    public static RouteGroupBuilder MapGamesEndpoints(this WebApplication app)
    {
        var group = app.MapGroup("games")
                       .WithParameterValidation();               

        // GET /games : liste tous les jeux avec leurs genres
        group.MapGet("/", async (GameStoreContext dbContext) =>
        {
            var games = await dbContext.Games
                .Include(g => g.Genre)
                .Select(g => g.ToGameDetailsDto())
                .ToListAsync();

            return Results.Ok(games);
        });

        
         // POST /games : création d’un nouveau jeu
        group.MapPost("/", async (CreateGameDto newGame, GameStoreContext dbContext) =>
        {
            var game = newGame.ToEntity();
            dbContext.Games.Add(game);
            await dbContext.SaveChangesAsync();

            // Recharger avec Genre inclus
            var gameWithGenre = await dbContext.Games
                .Include(g => g.Genre)
                .FirstOrDefaultAsync(g => g.Id == game.Id);

            return Results.CreatedAtRoute(
                GetGameEndpointName,
                new { id = game.Id },
                gameWithGenre!.ToGameDetailsDto());
        });

        // PUT /games/{id} : mise à jour d’un jeu
        group.MapPut("/{id}", async (int id, UpdateGameDto updateGame, GameStoreContext dbContext) =>
        {
            var game = await dbContext.Games.FindAsync(id);
            if (game is null)
            {
                return Results.NotFound();
            }

            // Mise à jour des propriétés
            game.Name = updateGame.Name;
            game.GenreId = updateGame.GenreId;
            game.Price = updateGame.Price;
            game.ReleaseDate = updateGame.ReleaseDate;

            await dbContext.SaveChangesAsync();

            return Results.NoContent();
        });

        // DELETE /games/{id} : suppression d’un jeu
        group.MapDelete("/{id}", async (int id, GameStoreContext dbContext) =>
        {
            var game = await dbContext.Games.FindAsync(id);
            if (game is null)
            {
                return Results.NotFound();
            }

            dbContext.Games.Remove(game);
            await dbContext.SaveChangesAsync();

            return Results.NoContent();
        });

        // GET /games/{id} : récupérer un jeu par id avec son genre
        group.MapGet("/{id}", async (int id, GameStoreContext dbContext) =>
        {
            var game = await dbContext.Games
                .Include(g => g.Genre)
                .FirstOrDefaultAsync(g => g.Id == id);

            return game is null ? Results.NotFound() : Results.Ok(game.ToGameDetailsDto());
        })
        .WithName(GetGameEndpointName);

        return group;
    }
}
